@{
    ViewData["Title"] = "قوانین و مقررات";
}

<!-- Header -->
@await Html.PartialAsync("_SecHeader", "قوانین و مقررات")

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-5">
                    <!-- Header -->
                    <div class="text-center mb-5">
                        <h1 class="h2 mb-3" style="color: #a62626;">قوانین و مقررات</h1>
                        <p class="text-muted">لطفاً قوانین و مقررات استفاده از پلتفرم کلید را با دقت مطالعه کنید</p>
                    </div>

                    <!-- Terms Content -->
                    <div class="terms-content">
                        <div class="mb-4">
                            <h3 class="h4 text-dark mb-3">
                                <span class="badge me-2" style="background-color: #a62626;">۱</span>
                                کلیات
                            </h3>
                            <p class="text-muted lh-lg">
                                با استفاده از پلتفرم کلید، شما موافقت خود را با تمامی قوانین و مقررات ذکر شده در این صفحه اعلام می‌کنید. این قوانین ممکن است بدون اطلاع قبلی تغییر کنند و مسئولیت مطالعه آن‌ها بر عهده کاربران است.
                            </p>
                        </div>

                        <div class="mb-4">
                            <h3 class="h4 text-dark mb-3">
                                <span class="badge me-2" style="background-color: #a62626;">۲</span>
                                ثبت آگهی
                            </h3>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    تمامی آگهی‌ها باید حقیقی و مطابق با واقعیت باشند
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    استفاده از تصاویر مرتبط و با کیفیت الزامی است
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    ثبت آگهی‌های تکراری یا اسپم ممنوع است
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    ثبت آگهی‌های غیرقانونی یا مغایر با اخلاق عمومی ممنوع است
                                </li>
                            </ul>
                        </div>

                        <div class="mb-4">
                            <h3 class="h4 text-dark mb-3">
                                <span class="badge me-2" style="background-color: #a62626;">۳</span>
                                مسئولیت‌های کاربران
                            </h3>
                            <p class="text-muted lh-lg">
                                کاربران مسئول صحت اطلاعات ارائه شده در آگهی‌های خود هستند. پلتفرم کلید هیچ مسئولیتی در قبال صحت اطلاعات آگهی‌ها ندارد و تنها نقش واسطه را ایفا می‌کند.
                            </p>
                        </div>

                        <div class="mb-4">
                            <h3 class="h4 text-dark mb-3">
                                <span class="badge me-2" style="background-color: #a62626;">۴</span>
                                حریم خصوصی
                            </h3>
                            <p class="text-muted lh-lg">
                                اطلاعات شخصی کاربران محفوظ نگهداری می‌شود و تنها برای بهبود خدمات استفاده خواهد شد. این اطلاعات بدون اجازه کاربران در اختیار اشخاص ثالث قرار نخواهد گرفت.
                            </p>
                        </div>

                        <div class="mb-4">
                            <h3 class="h4 text-dark mb-3">
                                <span class="badge me-2" style="background-color: #a62626;">۵</span>
                                قوانین معاملات
                            </h3>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    تمامی معاملات بین خریدار و فروشنده انجام می‌شود
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    پلتفرم کلید مسئولیتی در قبال نتیجه معاملات ندارد
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    توصیه می‌شود قبل از پرداخت، کالا را بررسی کنید
                                </li>
                            </ul>
                        </div>

                        <div class="mb-4">
                            <h3 class="h4 text-dark mb-3">
                                <span class="badge me-2" style="background-color: #a62626;">۶</span>
                                تخلفات و جریمه‌ها
                            </h3>
                            <div class="alert alert-warning">
                                <h5 class="alert-heading">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    موارد منجر به مسدودی حساب کاربری:
                                </h5>
                                <ul class="mb-0">
                                    <li>ثبت آگهی‌های جعلی یا کلاهبرداری</li>
                                    <li>استفاده از زبان نامناسب در توضیحات</li>
                                    <li>تلاش برای فریب سایر کاربران</li>
                                    <li>نقض مکرر قوانین پلتفرم</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h3 class="h4 text-dark mb-3">
                                <span class="badge me-2" style="background-color: #a62626;">۷</span>
                                تغییرات قوانین
                            </h3>
                            <p class="text-muted lh-lg">
                                پلتفرم کلید حق تغییر این قوانین را در هر زمان محفوظ می‌دارد. تغییرات از طریق اعلان در سایت به اطلاع کاربران خواهد رسید.
                            </p>
                        </div>

                        <!-- Contact for Questions -->
                        <div class="bg-light rounded p-4 text-center">
                            <h4 class="h5 mb-3">سوالی دارید؟</h4>
                            <p class="text-muted mb-3">در صورت داشتن سوال در مورد قوانین و مقررات، با ما تماس بگیرید</p>
                            <a asp-controller="Info" asp-action="Contact" class="btn" style="background-color: #a62626; color: white; border-color: #a62626;">
                                <i class="fas fa-envelope me-2"></i>
                                تماس با ما
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
@await Html.PartialAsync("_Footer")

<!-- Mobile Navigation -->
@await Html.PartialAsync("_MobileNav")
