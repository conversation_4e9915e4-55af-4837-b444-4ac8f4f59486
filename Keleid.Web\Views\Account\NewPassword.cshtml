@await Html.PartialAsync("_Account")

<div class="login-container">
    <div class="login-box">
        <div class="login-header">
            <div class="logo">
                <a asp-controller="Home" asp-action="Index"><img src="/assets/img/logo.png" alt="لوگو" class="logo-img"></a>
            </div>
        </div>

        <div class="auth-section">
            <h4>رمز عبور جدید خود را وارد کنید</h4>
            <p class="verification-desc">لطفا رمز عبور جدید خود را انتخاب کنید</p>
            
            <form id="newPasswordForm" class="login-form" autocomplete="on">
                @Html.AntiForgeryToken()

                <!-- فیلد مخفی برای شماره تلفن به عنوان username -->
                <input type="text" id="username" name="username" autocomplete="username" style="position: absolute; left: -9999px; opacity: 0;" tabindex="-1">

                <div class="mb-3">
                    <div class="password-input">
                        <input type="password" id="newPassword" name="newPassword" placeholder="رمز عبور جدید" autocomplete="new-password" required>
                    </div>
                    <div class="error-message" id="newPasswordError"></div>
                </div>

                <div class="mb-3">
                    <div class="password-input">
                        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="تکرار رمز عبور" autocomplete="new-password" required>
                    </div>
                    <div class="error-message" id="confirmPasswordError"></div>
                </div>

                <div class="password-requirements">
                    <p class="text-muted small mb-3">رمز عبور باید حداقل ۶ کاراکتر باشد</p>
                </div>

                <button type="submit" class="btn btn-danger w-100" id="submitBtn">
                    <span id="submitBtnText">تایید رمز عبور</span>
                    <i class="bi bi-arrow-left"></i>
                </button>
            </form>
        </div>
    </div>
</div>

@section js {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('newPasswordForm');
            const newPasswordInput = document.getElementById('newPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const usernameInput = document.getElementById('username');
            const newPasswordError = document.getElementById('newPasswordError');
            const confirmPasswordError = document.getElementById('confirmPasswordError');
            const submitBtn = document.getElementById('submitBtn');
            const submitBtnText = document.getElementById('submitBtnText');

            // دریافت شماره تلفن از server-side
            const phoneNumber = '@Context.Session.GetString("PhoneNumber")';

            // پر کردن فیلد username مخفی برای مرورگر
            if (phoneNumber) {
                usernameInput.value = phoneNumber;
            }

            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Clear previous errors
                newPasswordError.textContent = '';
                newPasswordError.classList.remove('show');
                confirmPasswordError.textContent = '';
                confirmPasswordError.classList.remove('show');

                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                // اعتبارسنجی شماره تلفن
                if (!phoneNumber) {
                    newPasswordError.textContent = 'شماره تلفن یافت نشد';
                    newPasswordError.classList.add('show');
                    return;
                }

                // اعتبارسنجی طول پسورد
                if (newPassword.length < 6) {
                    newPasswordError.textContent = 'رمز عبور باید حداقل ۶ کاراکتر باشد';
                    newPasswordError.classList.add('show');
                    return;
                }

                // اعتبارسنجی تطابق پسوردها
                if (newPassword !== confirmPassword) {
                    confirmPasswordError.textContent = 'رمز عبور و تکرار آن یکسان نیستند';
                    confirmPasswordError.classList.add('show');
                    return;
                }

                // Disable button and show loading
                submitBtn.disabled = true;
                submitBtnText.textContent = 'در حال تنظیم...';

                // ارسال درخواست AJAX
                fetch('@Url.Action("SetNewPassword", "Account")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    },
                    body: `phoneNumber=${encodeURIComponent(phoneNumber)}&newPassword=${encodeURIComponent(newPassword)}&confirmPassword=${encodeURIComponent(confirmPassword)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // موفقیت، هدایت به صفحه مقصد
                        window.location.href = data.redirectUrl;
                    } else {
                        newPasswordError.textContent = data.message || 'خطایی رخ داده است';
                        newPasswordError.classList.add('show');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    newPasswordError.textContent = 'خطای ارتباط با سرور';
                    newPasswordError.classList.add('show');
                })
                .finally(() => {
                    // Re-enable button
                    submitBtn.disabled = false;
                    submitBtnText.textContent = 'تایید رمز عبور';
                });
            });

            // اعتبارسنجی real-time برای تطابق پسورد
            confirmPasswordInput.addEventListener('input', function() {
                const newPassword = newPasswordInput.value;
                const confirmPassword = this.value;

                confirmPasswordError.classList.remove('show');

                if (confirmPassword && newPassword !== confirmPassword) {
                    confirmPasswordError.textContent = 'رمز عبور و تکرار آن یکسان نیستند';
                    confirmPasswordError.classList.add('show');
                } else {
                    confirmPasswordError.textContent = '';
                    confirmPasswordError.classList.remove('show');
                }
            });

            // اعتبارسنجی real-time برای طول پسورد
            newPasswordInput.addEventListener('input', function() {
                const newPassword = this.value;

                newPasswordError.classList.remove('show');

                if (newPassword && newPassword.length < 6) {
                    newPasswordError.textContent = 'رمز عبور باید حداقل ۶ کاراکتر باشد';
                    newPasswordError.classList.add('show');
                } else {
                    newPasswordError.textContent = '';
                    newPasswordError.classList.remove('show');
                }
            });
        });
    </script>
}
