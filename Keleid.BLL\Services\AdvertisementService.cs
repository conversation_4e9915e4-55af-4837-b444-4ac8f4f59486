using Keleid.BLL.DTOs;
using Keleid.BLL.Services.Interfaces;
using Keleid.DAL;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Keleid.BLL.Services
{
    public class AdvertisementService : IAdvertisementService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AdvertisementService> _logger;
        private readonly NotificationService _notificationService;
        private readonly UserTaskService _taskService;

        public AdvertisementService(ApplicationDbContext context, ILogger<AdvertisementService> logger,
            NotificationService notificationService, UserTaskService taskService)
        {
            _context = context;
            _logger = logger;
            _notificationService = notificationService;
            _taskService = taskService;
        }

        public async Task<List<AdvertisementDto>> GetApprovedAdvertisementsAsync()
        {
            try
            {
                var advertisements = await _context.Advertisements
                    .Where(a => a.IsApproved && !a.IsDeleted)
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync();

                return advertisements.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approved advertisements");
                return new List<AdvertisementDto>();
            }
        }

        public async Task<List<AdvertisementDto>> GetPendingAdvertisementsAsync()
        {
            try
            {
                var advertisements = await _context.Advertisements
                    .Where(a => !a.IsApproved && !a.IsDeleted)
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync();

                return advertisements.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending advertisements");
                return new List<AdvertisementDto>();
            }
        }

        public async Task<List<AdvertisementDto>> GetAdvertisementsByCategoryAsync(int categoryId)
        {
            try
            {
                var advertisements = await _context.Advertisements
                    .Where(a => a.CategoryId == categoryId && a.IsApproved && !a.IsDeleted)
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync();

                return advertisements.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advertisements by category {CategoryId}", categoryId);
                return new List<AdvertisementDto>();
            }
        }

        public async Task<List<AdvertisementDto>> GetAdvertisementsByCityAsync(string city)
        {
            try
            {
                var advertisements = await _context.Advertisements
                    .Where(a => a.Location.City == city && a.IsApproved && !a.IsDeleted)
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync();

                return advertisements.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advertisements by city {City}", city);
                return new List<AdvertisementDto>();
            }
        }

        public async Task<AdvertisementDto?> GetAdvertisementByIdAsync(int id)
        {
            try
            {
                var advertisement = await _context.Advertisements
                    .Where(a => a.Id == id && a.IsApproved && !a.IsDeleted)
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .FirstOrDefaultAsync();

                return advertisement != null ? MapToDto(advertisement) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advertisement by id {Id}", id);
                return null;
            }
        }

        public async Task<AdvertisementDto?> GetAdvertisementByIdForAdminAsync(int id)
        {
            try
            {
                var advertisement = await _context.Advertisements
                    .Where(a => a.Id == id) // بدون فیلتر وضعیت - همه آگهی‌ها برای ادمین
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.User) // شامل اطلاعات کاربر
                    .Include(a => a.ApprovedByUser) // شامل اطلاعات ادمین تایید کننده
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .FirstOrDefaultAsync();

                return advertisement != null ? MapToDto(advertisement) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advertisement by id {Id} for admin", id);
                return null;
            }
        }

        public async Task<AdvertisementDto?> GetAdvertisementByIdAndSlugAsync(int id, string slug)
        {
            try
            {
                var advertisement = await _context.Advertisements
                    .Where(a => a.Id == id && a.Slug == slug && a.IsApproved && !a.IsDeleted)
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .FirstOrDefaultAsync();

                return advertisement != null ? MapToDto(advertisement) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advertisement by id {Id} and slug {Slug}", id, slug);
                return null;
            }
        }

        public async Task<List<AdvertisementDto>> GetUserAdvertisementsAsync(string userId)
        {
            try
            {
                var advertisements = await _context.Advertisements
                    .Where(a => a.UserId == userId && !a.IsDeleted)
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync();

                return advertisements.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user advertisements for user {UserId}", userId);
                return new List<AdvertisementDto>();
            }
        }

        public async Task<(List<AdvertisementDto> advertisements, int totalCount, int totalPages)> GetUserAdvertisementsWithPaginationAsync(string userId, int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var query = _context.Advertisements
                    .Where(a => a.UserId == userId) // شامل آگهی‌های حذف شده هم می‌شود
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature);

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var advertisements = await query
                    .OrderByDescending(a => a.CreatedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (advertisements.Select(MapToDto).ToList(), totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user advertisements with pagination for user {UserId}", userId);
                return (new List<AdvertisementDto>(), 0, 0);
            }
        }

        public async Task<bool> SoftDeleteAdvertisementAsync(int advertisementId, string userId)
        {
            try
            {
                var advertisement = await _context.Advertisements
                    .FirstOrDefaultAsync(a => a.Id == advertisementId && a.UserId == userId);

                if (advertisement == null)
                {
                    _logger.LogWarning("Advertisement with ID {AdvertisementId} not found for user {UserId}", advertisementId, userId);
                    return false;
                }

                if (advertisement.IsDeleted)
                {
                    _logger.LogWarning("Advertisement with ID {AdvertisementId} is already deleted", advertisementId);
                    return false;
                }

                advertisement.IsDeleted = true;
                await _context.SaveChangesAsync();

                _logger.LogInformation("Advertisement with ID {AdvertisementId} soft deleted by user {UserId}", advertisementId, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error soft deleting advertisement {AdvertisementId} for user {UserId}", advertisementId, userId);
                return false;
            }
        }

        public async Task<(List<FavoriteDto> favorites, int totalCount, int totalPages)> GetUserFavoritesWithPaginationAsync(string userId, int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var query = _context.Favorites
                    .Where(f => f.UserId == userId)
                    .Include(f => f.Advertisement)
                        .ThenInclude(a => a.Category)
                    .Include(f => f.Advertisement)
                        .ThenInclude(a => a.Location)
                    .Include(f => f.Advertisement)
                        .ThenInclude(a => a.ContactInfo)
                    .Include(f => f.Advertisement)
                        .ThenInclude(a => a.Images)
                    .Include(f => f.Advertisement)
                        .ThenInclude(a => a.Features)
                            .ThenInclude(af => af.CategoryFeature)
                    .Where(f => f.Advertisement.IsApproved && !f.Advertisement.IsDeleted); // فقط آگهی‌های تایید شده و غیر حذف شده

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var favorites = await query
                    .OrderByDescending(f => f.CreatedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (favorites.Select(MapToFavoriteDto).ToList(), totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user favorites with pagination for user {UserId}", userId);
                return (new List<FavoriteDto>(), 0, 0);
            }
        }

        public async Task<bool> RemoveFavoriteAsync(int advertisementId, string userId)
        {
            try
            {
                var favorite = await _context.Favorites
                    .FirstOrDefaultAsync(f => f.AdId == advertisementId && f.UserId == userId);

                if (favorite == null)
                {
                    _logger.LogWarning("Favorite not found for advertisement {AdvertisementId} and user {UserId}", advertisementId, userId);
                    return false;
                }

                _context.Favorites.Remove(favorite);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Favorite removed for advertisement {AdvertisementId} by user {UserId}", advertisementId, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing favorite for advertisement {AdvertisementId} and user {UserId}", advertisementId, userId);
                return false;
            }
        }

        public async Task<bool> IsAdvertisementFavoritedAsync(int advertisementId, string userId)
        {
            try
            {
                if (string.IsNullOrEmpty(userId))
                    return false;

                return await _context.Favorites
                    .AnyAsync(f => f.AdId == advertisementId && f.UserId == userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if advertisement {AdvertisementId} is favorited by user {UserId}", advertisementId, userId);
                return false;
            }
        }

        public async Task<bool> AddFavoriteAsync(int advertisementId, string userId)
        {
            try
            {
                // بررسی اینکه آگهی وجود دارد و تایید شده است
                var advertisement = await _context.Advertisements
                    .FirstOrDefaultAsync(a => a.Id == advertisementId && a.IsApproved && !a.IsDeleted);

                if (advertisement == null)
                {
                    _logger.LogWarning("Advertisement {AdvertisementId} not found or not approved", advertisementId);
                    return false;
                }

                // بررسی اینکه قبلاً نشان نشده باشد
                var existingFavorite = await _context.Favorites
                    .FirstOrDefaultAsync(f => f.AdId == advertisementId && f.UserId == userId);

                if (existingFavorite != null)
                {
                    _logger.LogWarning("Advertisement {AdvertisementId} is already favorited by user {UserId}", advertisementId, userId);
                    return true; // قبلاً نشان شده
                }

                var favorite = new Keleid.DAL.Models.Favorite
                {
                    AdId = advertisementId,
                    UserId = userId,
                    CreatedAt = DateTime.Now
                };

                _context.Favorites.Add(favorite);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Advertisement {AdvertisementId} added to favorites by user {UserId}", advertisementId, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding advertisement {AdvertisementId} to favorites for user {UserId}", advertisementId, userId);
                return false;
            }
        }

        public async Task<bool> ToggleFavoriteAsync(int advertisementId, string userId)
        {
            try
            {
                var isFavorited = await IsAdvertisementFavoritedAsync(advertisementId, userId);

                if (isFavorited)
                {
                    await RemoveFavoriteAsync(advertisementId, userId);
                    return false; // حذف شد
                }
                else
                {
                    await AddFavoriteAsync(advertisementId, userId);
                    return true; // اضافه شد
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling favorite for advertisement {AdvertisementId} and user {UserId}", advertisementId, userId);
                return false;
            }
        }



        public async Task<List<AdvertisementDto>> GetLatestAdvertisementsAsync(int count, string? categorySlug = null, string? province = null, string? searchTerm = null)
        {
            try
            {
                var query = _context.Advertisements
                    .Include(a => a.Location)
                    .Where(a => a.IsApproved && !a.IsDeleted);

                // فیلتر بر اساس استان
                if (!string.IsNullOrEmpty(province) && province != "کل ایران")
                {
                    query = query.Where(a => a.Location.Province == province);
                }

                // فیلتر بر اساس دسته‌بندی
                if (!string.IsNullOrEmpty(categorySlug))
                {
                    var cat = await _context.Categories
                        .Where(a => a.Slug == categorySlug)
                        .FirstOrDefaultAsync();

                    if (cat != null)
                    {
                        var subCats = await _context.Categories
                            .Where(a => a.ParentCategoryId == cat.Id)
                            .ToListAsync();

                        subCats.Add(cat); // اضافه کردن دسته اصلی

                        var categoryIds = subCats.Select(c => c.Id).ToList();
                        query = query.Where(ad => categoryIds.Contains(ad.CategoryId));
                    }
                }

                // فیلتر بر اساس عبارت جستجو
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(a => a.Title.Contains(searchTerm) || a.Description.Contains(searchTerm));
                }

                var advertisements = await query
                    .Include(a => a.Category)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .OrderByDescending(a => a.CreatedAt)
                    .Take(count)
                    .ToListAsync();

                return advertisements.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting latest advertisements with province filter");
                return new List<AdvertisementDto>();
            }
        }

        public async Task<List<AdvertisementDto>> SearchAdvertisementsAsync(string searchTerm)
        {
            try
            {
                var advertisements = await _context.Advertisements
                    .Where(a => a.IsApproved && !a.IsDeleted &&
                               (a.Title.Contains(searchTerm) || a.Description.Contains(searchTerm)))
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync();

                return advertisements.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching advertisements with term {SearchTerm}", searchTerm);
                return new List<AdvertisementDto>();
            }
        }

        public async Task<(List<AdvertisementDto> advertisements, int totalCount, bool hasMore)> GetLatestAdvertisementsWithPaginationAsync(int pageNumber = 1, int pageSize = 6, string? categorySlug = null, string? province = null, string? searchTerm = null)
        {
            try
            {
                var query = _context.Advertisements
                    .Include(a => a.Location)
                    .Where(a => a.IsApproved && !a.IsDeleted);

                // فیلتر بر اساس استان
                if (!string.IsNullOrEmpty(province) && province != "کل ایران")
                {
                    query = query.Where(a => a.Location.Province == province);
                }

                // فیلتر بر اساس دسته‌بندی
                if (!string.IsNullOrEmpty(categorySlug))
                {
                    var cat = await _context.Categories
                        .Where(a => a.Slug == categorySlug)
                        .FirstOrDefaultAsync();

                    if (cat != null)
                    {
                        var subCats = await _context.Categories
                            .Where(a => a.ParentCategoryId == cat.Id)
                            .ToListAsync();

                        subCats.Add(cat); // اضافه کردن دسته اصلی

                        var categoryIds = subCats.Select(c => c.Id).ToList();
                        query = query.Where(ad => categoryIds.Contains(ad.CategoryId));
                    }
                }

                // فیلتر بر اساس عبارت جستجو
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(a => a.Title.Contains(searchTerm) || a.Description.Contains(searchTerm));
                }

                var totalCount = await query.CountAsync();
                var skip = (pageNumber - 1) * pageSize;

                var advertisements = await query
                    .Include(a => a.Category)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .OrderByDescending(a => a.CreatedAt)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                var hasMore = (skip + pageSize) < totalCount;

                return (advertisements.Select(MapToDto).ToList(), totalCount, hasMore);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting latest advertisements with pagination");
                return (new List<AdvertisementDto>(), 0, false);
            }
        }

        public async Task<(List<AdvertisementDto> advertisements, int totalCount)> GetFilteredAdvertisementsAsync(
            int? categoryId = null,
            string? city = null,
            string? province = null,
            long? minPrice = null,
            long? maxPrice = null,
            string? searchTerm = null,
            int pageNumber = 1,
            int pageSize = 20)
        {
            try
            {
                var query = _context.Advertisements
                    .Where(a => a.IsApproved && !a.IsDeleted);

                // Apply filters
                if (categoryId.HasValue)
                    query = query.Where(a => a.CategoryId == categoryId.Value);

                if (!string.IsNullOrEmpty(city))
                    query = query.Where(a => a.Location.City == city);

                if (!string.IsNullOrEmpty(province))
                    query = query.Where(a => a.Location.Province == province);

                if (minPrice.HasValue)
                    query = query.Where(a => a.IsPriceless || a.Price >= minPrice.Value);

                if (maxPrice.HasValue)
                    query = query.Where(a => a.IsPriceless || a.Price <= maxPrice.Value);

                if (!string.IsNullOrEmpty(searchTerm))
                    query = query.Where(a => a.Title.Contains(searchTerm) || a.Description.Contains(searchTerm));

                var totalCount = await query.CountAsync();

                var advertisements = await query
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature)
                    .OrderByDescending(a => a.CreatedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (advertisements.Select(MapToDto).ToList(), totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting filtered advertisements");
                return (new List<AdvertisementDto>(), 0);
            }
        }

        /// <summary>
        /// تبدیل مدل Favorite به DTO
        /// </summary>
        private FavoriteDto MapToFavoriteDto(Keleid.DAL.Models.Favorite favorite)
        {
            return new FavoriteDto
            {
                Id = favorite.Id,
                UserId = favorite.UserId,
                AdId = favorite.AdId,
                CreatedAt = favorite.CreatedAt,
                Advertisement = MapToDto(favorite.Advertisement)
            };
        }

        public async Task<int> CreateAdvertisementAsync(Keleid.DAL.Models.Advertisement advertisement)
        {
            try
            {
                _context.Advertisements.Add(advertisement);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Advertisement created successfully with ID {AdvertisementId}", advertisement.Id);

                // ایجاد اعلان و وظیفه برای ادمین
                await CreateAdminNotificationsAsync(advertisement);

                return advertisement.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating advertisement");
                return 0;
            }
        }

        private async Task CreateAdminNotificationsAsync(Keleid.DAL.Models.Advertisement advertisement)
        {
            try
            {
                // دریافت اطلاعات دسته‌بندی
                var category = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Id == advertisement.CategoryId);

                var categoryTitle = category?.Title ?? "نامشخص";

                // ایجاد اعلان
                await _notificationService.CreateNewAdvertisementNotificationAsync(
                    advertisement.Id,
                    categoryTitle,
                    advertisement.Title);

                // ایجاد وظیفه تایید
                await _taskService.CreateAdvertisementApprovalTaskAsync(
                    advertisement.Id,
                    categoryTitle);

                _logger.LogInformation("Admin notifications created for advertisement {AdvertisementId}", advertisement.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating admin notifications for advertisement {AdvertisementId}", advertisement.Id);
                // عدم ایجاد اعلان نباید مانع ایجاد آگهی شود
            }
        }

        public async Task<bool> AddImageAsync(Keleid.DAL.Models.AdImage image)
        {
            try
            {
                _context.AdImages.Add(image);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Image added successfully for advertisement {AdvertisementId}", image.AdvertisementId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding image for advertisement {AdvertisementId}", image.AdvertisementId);
                return false;
            }
        }

        public async Task<(List<AdvertisementDto> advertisements, int totalCount, int totalPages)> GetAllAdvertisementsWithPaginationAsync(int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var query = _context.Advertisements
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.User)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature);

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var advertisements = await query
                    .OrderByDescending(a => a.CreatedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (advertisements.Select(MapToDto).ToList(), totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all advertisements with pagination");
                return (new List<AdvertisementDto>(), 0, 0);
            }
        }

        public async Task<(List<AdvertisementDto> advertisements, int totalCount, int totalPages)> GetPendingAdvertisementsWithPaginationAsync(int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var query = _context.Advertisements
                    .Where(a => !a.IsApproved && !a.IsDeleted) // فقط آگهی‌های در انتظار تایید
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.User)
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature);

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var advertisements = await query
                    .OrderByDescending(a => a.CreatedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (advertisements.Select(MapToDto).ToList(), totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending advertisements with pagination");
                return (new List<AdvertisementDto>(), 0, 0);
            }
        }

        public async Task<(List<AdvertisementDto> advertisements, int totalCount, int totalPages)> GetApprovedAdvertisementsWithPaginationAsync(int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var query = _context.Advertisements
                    .Where(a => a.IsApproved && !a.IsDeleted) // فقط آگهی‌های تایید شده
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.User)
                    .Include(a => a.ApprovedByUser) // شامل اطلاعات ادمین تایید کننده
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature);

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var advertisements = await query
                    .OrderByDescending(a => a.CreatedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (advertisements.Select(MapToDto).ToList(), totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approved advertisements with pagination");
                return (new List<AdvertisementDto>(), 0, 0);
            }
        }

        public async Task<(List<AdvertisementDto> advertisements, int totalCount, int totalPages)> GetDeletedAdvertisementsWithPaginationAsync(int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var query = _context.Advertisements
                    .Where(a => a.IsDeleted) // فقط آگهی‌های حذف شده
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .Include(a => a.User)
                    .Include(a => a.ApprovedByUser) // شامل اطلاعات ادمین
                    .Include(a => a.Features)
                        .ThenInclude(f => f.CategoryFeature);

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var advertisements = await query
                    .OrderByDescending(a => a.CreatedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (advertisements.Select(MapToDto).ToList(), totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting deleted advertisements with pagination");
                return (new List<AdvertisementDto>(), 0, 0);
            }
        }

        public async Task<bool> ApproveAdvertisementAsync(int advertisementId, string? approvedByUserId = null)
        {
            try
            {
                var advertisement = await _context.Advertisements.FindAsync(advertisementId);
                if (advertisement != null)
                {
                    advertisement.IsApproved = true;
                    advertisement.ApprovedByUserId = approvedByUserId;
                    await _context.SaveChangesAsync();
                    _logger.LogInformation("Advertisement {AdvertisementId} approved by user {UserId}", advertisementId, approvedByUserId);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving advertisement {AdvertisementId}", advertisementId);
                return false;
            }
        }

        public async Task<bool> RejectAdvertisementAsync(int advertisementId, string? rejectedByUserId = null)
        {
            try
            {
                var advertisement = await _context.Advertisements.FindAsync(advertisementId);
                if (advertisement != null)
                {
                    advertisement.IsApproved = false;
                    advertisement.ApprovedByUserId = rejectedByUserId; // ثبت کسی که رد کرده
                    await _context.SaveChangesAsync();
                    _logger.LogInformation("Advertisement {AdvertisementId} rejected by user {UserId}", advertisementId, rejectedByUserId);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting advertisement {AdvertisementId}", advertisementId);
                return false;
            }
        }

        /// <summary>
        /// تبدیل مدل Advertisement به DTO
        /// </summary>
        private AdvertisementDto MapToDto(Keleid.DAL.Models.Advertisement advertisement)
        {
            return new AdvertisementDto
            {
                Id = advertisement.Id,
                Title = advertisement.Title,
                Slug = advertisement.Slug,
                Description = advertisement.Description,
                Price = advertisement.Price,
                IsPriceless = advertisement.IsPriceless,
                IsDeleted = advertisement.IsDeleted,
                IsApproved = advertisement.IsApproved,
                CreatedAt = advertisement.CreatedAt,
                CategoryId = advertisement.CategoryId,
                CategoryTitle = advertisement.Category?.Title ?? "",
                CategorySlug = advertisement.Category?.Slug ?? "",
                LocationId = advertisement.LocationId,
                Province = advertisement.Location?.Province ?? "",
                City = advertisement.Location?.City ?? "",
                Address = advertisement.Location?.Addresss ?? "",
                ContactPhone = advertisement.ContactInfo?.PhoneNumber ?? "",
                ContactEmail = advertisement.ContactInfo?.Email ?? "",
                UserId = advertisement.UserId,
                UserPhone = advertisement.User?.PhoneNumber ?? "نامشخص",
                UserEmail = advertisement.User?.Email ?? "نامشخص",
                UserRegistrationDate = advertisement.User?.RegisterDate ?? DateTime.MinValue,
                ApprovedByUserId = advertisement.ApprovedByUserId,
                ApprovedByUserName = advertisement.ApprovedByUser?.UserName ?? (advertisement.ApprovedByUserId != null ? "نامشخص" : null),
                Images = advertisement.Images?.Select(img => new AdImageDto
                {
                    Id = img.Id,
                    AdvertisementId = img.AdvertisementId,
                    ImageUrl = img.ImageUrl,
                    IsMain = img.IsMain
                }).ToList() ?? new List<AdImageDto>(),
                MainImageUrl = advertisement.Images?.FirstOrDefault(img => img.IsMain)?.ImageUrl ??
                             advertisement.Images?.FirstOrDefault()?.ImageUrl ??
                             "/assets/img/placeholder.png",
                Features = advertisement.Features?.Select(f => new AdvertisementFeatureDto
                {
                    Id = f.Id,
                    AdvertisementId = f.AdvertisementId,
                    CategoryFeatureId = f.CategoryFeatureId,
                    FeatureTitle = f.CategoryFeature?.Title ?? "",
                    Value = f.Value,
                    InputType = f.CategoryFeature?.InputType ?? "",
                    Options = f.CategoryFeature?.Options
                }).ToList() ?? new List<AdvertisementFeatureDto>()
            };
        }
    }
}
