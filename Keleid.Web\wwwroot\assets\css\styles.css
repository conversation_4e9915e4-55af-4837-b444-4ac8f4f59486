@font-face {
    font-family: 'IRANSans';
    src: url('../fonts/IRANSansWeb.woff2') format('woff2');
}

body {
    font-family: 'IRANSans', system-ui, -apple-system, sans-serif;
    background-color: #f5f5f5;
    overflow-x: hidden;
}

/* Navbar Styles */
.navbar {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.navbar .btn-danger {
    background-color: #a62626;
    border-color: #a62626;
}

.navbar .search-box .form-control {
    border-radius: 4px;
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
}

.navbar .search-box .input-group-text {
    background-color: transparent;
    border-right: none;
}

.navbar .search-box .form-control:focus {
    box-shadow: none;
    border-color: #ced4da;
}

.clear-search-btn {
    font-size: 0.9rem;
    opacity: 0.6;
    transition: all 0.2s ease;
    line-height: 1;
    cursor: pointer;
    z-index: 10;
    position: relative;
}

.clear-search-btn:hover {
    opacity: 1;
    color: #a62626 !important;
    transform: scale(1.1);
}

.clear-search-btn:focus {
    outline: none;
    box-shadow: none;
}

.input-group-text.d-flex {
    align-items: center;
}

.search-divider {
    width: 1px;
    height: 20px;
    background-color: #dee2e6;
    margin: 0 0.5rem;
}

.nav-link {
    color: #6c757d !important;
    font-size: 0.9rem;
}

.nav-link:hover {
    color: #a62626 !important;
}

.navbar-brand {
    font-size: 1rem;
}

.btn-danger {
    background-color: #a62626;
    border-color: #a62626;
    font-size: 0.9rem;
}

.btn-danger:hover {
    background-color: #a62626;
    border-color: #a62626;
}

.text-danger {
    color: #a62626 !important;
}

.bg-danger {
    background-color: #a62626 !important;
}

.dropdown-toggle {
    font-size: 0.9rem;
    color: #6c757d !important;
}

/* Main Content Spacing */
.container .mobile-categories {
    margin-top: 1rem;
}

.container .row {
    margin-top: 1rem;
}

/* Categories Sidebar */
.categories-sidebar {
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.category-item-wrapper {
    margin-bottom: 0.5rem;
}

.category-link {
    color: #464646;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex: 1;
}

.category-link:hover {
    color: #a62626;
}

.category-link i {
    margin-left: 0.75rem;
    width: 20px;
    text-align: center;
    color: #666;
}

.category-title {
    cursor: pointer;
    color: rgb(108, 117, 125);
    transition: color 0.2s;
    font-size: 0.9rem;
    color: #6c757d;
}
.category-title:hover {
    color: #a62626;
}

.btn-link.dropdown-toggle {
    color: #666;
    text-decoration: none;
    padding: 0.5rem;
    transition: transform 0.2s ease;
}

.btn-link.dropdown-toggle::after {
    display: none;
}

.btn-link.dropdown-toggle:hover {
    color: #464646;
}

.btn-link.dropdown-toggle[aria-expanded="true"] i {
    transform: rotate(180deg);
}

.subcategories {
    padding-right: 2.25rem;
}

.subcategory-link {
    color: #666;
    text-decoration: none;
    display: block;
    padding: 0.4rem 0;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.subcategory-link:hover {
    color: #a62626;
}

.subcategory-link.active {
    color: #a62626;
    font-weight: bold;
}

.category-title.active {
    color: #a62626;
    font-weight: bold;
}

/* Mobile Back Button */
.mobile-back-button {
    text-align: center;
}

/* Active state for mobile category items */
.category-item.active {
    background-color: #a62626 !important;
    color: white !important;
}

.category-item.active .category-name {
    color: white !important;
}

/* Ad Cards */
.ad-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    height: 100%;
}

.ad-card:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.ad-card .card-content {
    display: flex;
    align-items: stretch;
}

.ad-card .card-img-wrapper {
    flex: 0 0 auto;
    width: 120px;
    padding: 0.5rem;
}

.ad-card .card-img-top {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
}

.ad-card .card-body {
    flex: 1;
    padding: 1rem 1rem 1rem 0.5rem;
    display: flex;
    flex-direction: column;
}

.ad-card .card-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: #464646;
    flex: 1;
    font-weight: 700;
}

.ad-card .card-text {
    font-size: 0.8rem;
    color: #666;
    font-weight: 400;
}

.ad-card .price {
    font-size: 0.9rem;
    color: #464646;
    font-weight: 400;
    margin-top: auto;
    margin-bottom: 0.25rem;
}

.ad-card .location {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0;
    font-weight: 400;
}

/* Utility Classes */
.text-muted-custom {
    color: #666;
}

.btn-outline-secondary {
    border-color: #ddd;
    color: #464646;
}

.btn-outline-secondary:hover {
    background-color: #f5f5f5;
    border-color: #ddd;
    color: #464646;
}

/* Desktop Styles */
@media (min-width: 769px) {
    .ad-card {
        flex-direction: column;
    }

    .ad-card .card-content {
        flex-direction: column;
    }

    .ad-card .card-img-wrapper {
        width: 100%;
        padding: 0.75rem;
    }

    .ad-card .card-img-top {
        height: 200px;
        border-radius: 12px;
    }

    .ad-card .card-body {
        padding: 1rem;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    body {
        padding-bottom: 60px;
    }

    .container {
        padding: 0.75rem;
        margin: 0;
        max-width: 100%;
    }

    .row {
        margin: 0;
        --bs-gutter-x: 0;
    }

    .navbar {
        padding: 0.5rem;
        box-shadow: none;
        border-bottom: 1px solid #eee;
    }

    .mobile-header {
        display: flex !important;
        align-items: center;
        width: 100%;
        padding: 0;
        font-size: 0.9rem;
    }

    .mobile-header .d-flex {
        margin-right: 0.5rem;
        color: #6c757d;
    }

    .mobile-search {
        flex: 1;
        margin: 0 0.5rem;
    }

    .mobile-search .form-control {
        font-size: 0.9rem;
    }

    .mobile-search .input-group-text {
        color: #6c757d;
    }

    .mobile-categories {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 0.75rem;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #464646;
        text-align: center;
        padding: 0.75rem;
        border-radius: 8px;
        background-color: white;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
    }

    .category-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    }

    .category-icon {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .category-name {
        font-size: 0.75rem;
        margin-top: 0.25rem;
        color: #464646;
        font-weight: 500;
    }

    /* Category background colors */
    .bg-cat-1 { background-color: #FFE8E8; color: #A62626; }
    .bg-cat-2 { background-color: #FFE8FF; color: #A626A6; }
    .bg-cat-3 { background-color: #E8FFE8; color: #26A626; }
    .bg-cat-4 { background-color: #E8E8FF; color: #2626A6; }

    .mobile-nav {
        display: flex !important;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #eee;
        padding: 0.5rem;
        z-index: 1000;
        width: 100vw;
        margin: 0;
    }

    .mobile-nav-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #666;
        font-size: 0.7rem;
        padding: 0.25rem;
        text-align: center;
    }

    .mobile-nav-item.active {
        color: #A62626;
    }

    .mobile-nav-item i {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }

    .col-md-3 {
        display: none;
    }

    .col-md-9 {
        flex: 0 0 100%;
        max-width: 100%;
        padding: 0;
    }

    .ad-card {
        margin: 0;
        border-radius: 0;
        border-bottom: 2px solid #e0e0e0;
        box-shadow: none;
    }

    .ad-card:hover {
        transform: none;
        box-shadow: none;
    }

    .ad-card .card-img-wrapper {
        width: 100px;
        padding: 0.5rem;
        margin-left: 0.5rem;
    }

    .ad-card .card-img-top {
        height: 100px;
        border-radius: 6px;
    }

    .ad-card .card-body {
        padding: 0.75rem 0.75rem 0.75rem 0;
        min-width: 0;
    }

    .ad-card .card-title {
        padding-left: 0.5rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .ad-card .price,
    .ad-card .location {
        padding-left: 0.5rem;
    }

    .ad-card .card-content {
        align-items: flex-start;
    }

    .row.g-3 {
        --bs-gutter-x: 0;
        --bs-gutter-y: 0;
    }

    .row.g-3 > * {
        padding: 0;
        margin: 0;
        border-bottom: none;
    }

    .section-title {
        padding: 1rem;
        margin: 0;
        margin-top: 0.75rem;
        font-size: 0.9rem;
        border-bottom: 1px solid #eee;
        background: white;
    }

    .col-md-6:last-child .ad-card {
        border-bottom: none;
    }
}

@media (max-width: 576px) {
    .mobile-categories {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
        padding: 0.5rem;
    }
    
    .category-icon {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }
    
    .category-name {
        font-size: 0.7rem;
    }
}

/* Sidebar Footer */
.sidebar-footer {
    font-size: 0.8rem;
}

.sidebar-footer hr {
    border-color: #eee;
    margin: 1.5rem 0;
}

.footer-links a {
    text-decoration: none;
    color: #666;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: #464646;
}

.social-links a {
    text-decoration: none;
    font-size: 1.1rem;
}

.social-links a:hover {
    color: #464646 !important;
}

.trust-logos img {
    filter: grayscale(100%);
    opacity: 0.7;
    transition: all 0.2s ease;
}

.trust-logos img:hover {
    filter: grayscale(0%);
    opacity: 1;
}

/* Common Styles for Multi-step Listing Creation */

/* Step Indicator */
.step-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
}

.step {
    flex: 1;
    text-align: center;
    position: relative;
    padding: 0 10px;
}

.step-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    position: relative;
    z-index: 2;
}

.step.active .step-circle {
    background-color: #dc3545;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    color: #6c757d;
}

.step.active .step-title {
    color: #dc3545;
}

.step-line {
    position: absolute;
    top: 15px;
    height: 2px;
    background-color: #e9ecef;
    width: 100%;
    right: 0;
}

/* Header */
.listing-header {
    background-color: white;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
}

.listing-header .close-btn {
    text-decoration: none;
    color: #6c757d;
    margin-left: 1rem;
    font-size: 1.5rem;
    line-height: 1;
}

.listing-header .close-btn:hover {
    color: #dc3545;
}

.listing-header h4 {
    margin: 0;
    font-size: 1.25rem;
    color: #212529;
}

/* Form Elements */
.form-label.required::after {
    content: "*";
    color: #dc3545;
    margin-right: 4px;
}

/* Category List Styles */
.category-list {
    max-height: 400px;
    overflow-y: auto;
}

.category-item {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
    transition: background-color 0.2s;
}

.category-item:hover {
    background-color: #f8f9fa;
}

.category-item i {
    width: 24px;
    color: #6c757d;
}

.subcategory-list {
    display: none;
    padding-right: 2rem;
}

.subcategory-list.active {
    display: block;
}

.back-button {
    display: none;
}

.back-button.active {
    display: block;
}

/* Image Upload Styles */
.image-upload-container {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.2s;
}

.image-upload-container:hover {
    border-color: #dc3545;
}

.image-upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.image-preview-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
}

.image-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview-item .remove-image {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
}

.image-preview-item .main-image {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: rgba(255, 255, 255, 0.9);
    color: #dc3545;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

/* Location Map Styles */
#map {
    height: 300px;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.location-search {
    position: relative;
    margin-bottom: 1rem;
}

.location-search .form-control {
    padding-left: 40px;
}

.location-search .search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Contact Type Styles */
.contact-type {
    display: flex;
    margin-bottom: 1rem;
}

.contact-type-item {
    flex: 1;
    text-align: center;
    padding: 1rem;
    border: 1px solid #dee2e6;
    cursor: pointer;
    transition: all 0.2s;
}

.contact-type-item:first-child {
    border-radius: 0 8px 8px 0;
}

.contact-type-item:last-child {
    border-radius: 8px 0 0 8px;
}

.contact-type-item.active {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
}

.contact-type-item:not(.active):hover {
    background-color: #f8f9fa;
}

/* User Panel Styles */
.user-panel-sidebar {
    position: sticky;
    top: 80px;
}

.user-info {
    border-bottom: 1px solid #eee;
}

.panel-menu {
    display: flex;
    flex-direction: column;
}

.panel-menu-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    color: #464646;
    text-decoration: none;
    border-radius: 8px;
    margin-bottom: 0.25rem;
    transition: all 0.2s ease;
}

.panel-menu-item:hover {
    background-color: #f5f5f5;
    color: #464646;
}

.panel-menu-item.active {
    background-color: #f5f5f5;
    color: #a62626;
}

.panel-menu-item i {
    width: 24px;
    margin-left: 0.75rem;
    font-size: 1.1rem;
}

.panel-menu-item span {
    font-size: 0.9rem;
}

.panel-menu-item span.small {
    font-size: 0.8rem;
    margin-right: auto;
}

/* Mobile Panel Styles */
@media (max-width: 991px) {
    .user-panel-sidebar {
        margin-bottom: 1rem;
        position: static;
    }

    .panel-menu-item {
        padding: 1rem;
        border-bottom: 1px solid #eee;
        border-radius: 0;
        margin-bottom: 0;
    }

    .panel-menu-item:last-child {
        border-bottom: none;
    }

    .panel-menu-item.active {
        background-color: transparent;
        color: #a62626;
    }

    .panel-menu-item:hover {
        background-color: transparent;
    }
}

/* User Info List Styles */
.user-info-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-item {
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.info-label {
    font-size: 0.9rem;
}

.info-value {
    font-size: 1rem;
    color: #464646;
}

.bg-success-light {
    background-color: #e8f5e9 !important;
}

.form-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
    cursor: pointer;
}

.form-switch .form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

@media (max-width: 576px) {
    .info-item {
        padding-bottom: 1rem;
    }
    
    .user-info-list {
        gap: 1rem;
    }
    
    .info-label {
        font-size: 0.8rem;
    }
    
    .info-value {
        font-size: 0.9rem;
    }
}

/* My Ads List Styles */
.my-ads-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.ad-item {
    padding: 1.5rem;
    border: 1px solid #eee;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.ad-item:hover {
    border-color: #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.ad-item h6 {
    font-size: 1rem;
    color: #464646;
}

.ad-item .text-success {
    color: #28a745 !important;
}

.ad-item .dropdown-menu {
    min-width: 150px;
    padding: 0.5rem 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ad-item .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.ad-item .dropdown-item i {
    width: 16px;
    text-align: center;
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: 12px;
}

.modal-header .btn-close {
    margin: 0;
    padding: 1rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1rem 2rem 2rem;
}

.modal-footer .btn {
    min-width: 120px;
}

@media (max-width: 768px) {
    .ad-item {
        padding: 1rem;
    }

    .ad-item h6 {
        font-size: 0.9rem;
    }

    .my-ads-list {
        gap: 1rem;
    }
}

/* Footer Divider */
.footer-divider {
    width: 4px;
    height: 4px;
    background-color: #ddd;
    border-radius: 50%;
}

/* Favorite Ads List Styles */
.fav-ads-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.fav-ad-item {
    display: block;
    padding: 1.5rem;
    border: 1px solid #eee;
    border-radius: 8px;
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
}

.fav-ad-item:hover {
    border-color: #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    text-decoration: none;
    color: inherit;
}

.fav-ad-item h6 {
    font-size: 1rem;
    color: #464646;
}

.fav-ad-item .text-success {
    color: #28a745 !important;
}

.remove-fav {
    font-size: 1.1rem;
    transition: transform 0.2s ease;
}

.remove-fav:hover {
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .fav-ad-item {
        padding: 1rem;
    }

    .fav-ad-item h6 {
        font-size: 0.9rem;
    }

    .fav-ads-list {
        gap: 1rem;
    }
}

/* Custom Pagination Styles */
.custom-pagination .page-link {
    color: #6c757d;
    background-color: #fff;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    font-size: 0.9rem;
}

.custom-pagination .page-link:hover {
    color: #a62626;
    background-color: #f8f9fa;
    border-color: #a62626;
}

.custom-pagination .page-link:focus {
    box-shadow: 0 0 0 0.2rem rgba(166, 38, 38, 0.25);
    border-color: #a62626;
}

.custom-pagination .page-item.active .page-link {
    color: #fff;
    background-color: #a62626;
    border-color: #a62626;
}

.custom-pagination .page-item.active .page-link:hover {
    background-color: #8b1f1f;
    border-color: #8b1f1f;
}

.custom-pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    pointer-events: none;
    opacity: 0.6;
}

.custom-pagination .page-item:not(:first-child) .page-link {
    margin-right: 0;
}

/* Mobile pagination adjustments */
@media (max-width: 576px) {
    .custom-pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
        margin: 0 0.0625rem;
    }

    .custom-pagination .page-item {
        margin: 0 0.125rem;
    }
}

/* City Selection Modal Styles */
.city-search {
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 1;
}

.city-search .input-group {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.city-search .form-control {
    border: none;
    padding: 0.75rem 1rem;
}

.city-search .form-control:focus {
    box-shadow: none;
}

.city-search .input-group-text {
    border: none;
    color: #6c757d;
    padding: 0 1rem;
}

.city-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
}

.city-list .form-check {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.city-list .form-check:hover {
    background-color: #f8f9fa;
}

.city-list .form-check-input {
    cursor: pointer;
}

.city-list .form-check-input:checked {
    background-color: #a62626;
    border-color: #a62626;
}

.city-list .form-check-input:focus {
    border-color: #a62626;
    box-shadow: 0 0 0 0.25rem rgba(166, 38, 38, 0.25);
}

.city-list .form-check-input:not(:checked):hover {
    border-color: #a62626;
}

.city-list .form-check-label {
    cursor: pointer;
    font-size: 0.9rem;
    color: #464646;
}

.city-list .form-check-input:checked + .form-check-label {
    color: #a62626;
}

@media (max-width: 576px) {
    .city-list {
        max-height: 60vh;
    }
    
    .city-list .form-check {
        padding: 0.75rem 1rem;
    }
} 

/*Ads page styles*/
.gallery-image {
    width: 100%;
    height: 500px;
    object-fit: cover;
    margin-bottom: 1rem;
}

.thumbnail-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 0.5rem;
    border: 2px solid transparent;
}

    .thumbnail-image.active {
        border-color: #a62626;
    }

.more-images {
    width: 80px;
    height: 80px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    cursor: pointer;
    text-decoration: none;
}

    .more-images:hover {
        background: rgba(0, 0, 0, 0.8);
        color: white;
    }

.breadcrumb-wrapper {
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */
}

    .breadcrumb-wrapper::-webkit-scrollbar {
        display: none;
        /* Chrome, Safari, Opera */
    }

.breadcrumb {
    margin-bottom: 0;
    padding: 0;
    background: transparent;
    flex-wrap: nowrap;
}

.breadcrumb-item {
    display: inline-block;
    float: none;
}

    .breadcrumb-item a {
        color: #666;
        text-decoration: none;
    }

    .breadcrumb-item.active {
        color: #a62626;
    }

.btn-danger {
    background-color: #a62626;
    border-color: #a62626;
}

    .btn-danger:hover {
        background-color: #8a1f1f;
        border-color: #8a1f1f;
    }

.btn-outline-danger {
    color: #a62626;
    border-color: #a62626;
    width: 48px;
    height: 48px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

    .btn-outline-danger:hover {
        background-color: #a62626;
        border-color: #a62626;
        color: white;
    }

    .btn-outline-danger.active {
        background-color: #a62626;
        border-color: #a62626;
        color: white;
    }

hr {
    margin: 1rem 0;
    opacity: 0.1;
}

.text-justify {
    text-align: justify;
}

.feature-divider {
    height: 1px;
    background-color: rgba(0, 0, 0, 0.1);
    margin: 8px 0;
}

.vertical-divider {
    position: absolute;
    left: 0;
    top: 20%;
    bottom: 20%;
    width: 1px;
    background-color: rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .breadcrumb {
        font-size: 0.85rem;
    }

    .footer-links {
        width: 100%;
        justify-content: center;
        margin-top: 1rem;
    }

    .footer-divider {
        margin: 0 0.5rem;
    }
}

.bottom-sheet {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    border-radius: 16px 16px 0 0;
    transform: translateY(100%);
    transition: transform 0.3s ease-in-out;
    z-index: 1050;
    padding: 20px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

    .bottom-sheet.show {
        transform: translateY(0);
    }

.bottom-sheet-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out;
    z-index: 1040;
}

    .bottom-sheet-backdrop.show {
        opacity: 1;
        visibility: visible;
    }

.contact-option {
    display: flex;
    align-items: center;
    padding: 12px;
    text-decoration: none;
    color: inherit;
    transition: background-color 0.2s;
    border-radius: 8px;
}

    .contact-option:hover {
        background-color: #f8f9fa;
    }

    .contact-option i {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 12px;
        color: #a62626;
    }

    .contact-option .contact-label {
        color: #666;
        margin-left: 8px;
    }

    .contact-option .contact-value {
        font-weight: 500;
    }

.footer-divider {
    width: 1px;
    height: 16px;
    background-color: rgba(0, 0, 0, 0.1);
    display: inline-block;
}

footer {
    font-size: 0.85rem;
}

    footer .fab {
        font-size: 1.1rem;
    }

    footer img {
        height: 20px;
    }

i.fab.fa-github {
    font-size: 0.9rem;
}

.bookmark-icon {
    color: #a62626;
    font-size: 1.5rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    transition: all 0.2s ease;
}

    .bookmark-icon:hover {
        color: #a62626;
    }

        .bookmark-icon:hover i.far {
            font-weight: 900;
        }

    .bookmark-icon.active {
        color: #a62626;
    }

.action-icon {
    color: #666;
    font-size: 1.4rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    transition: all 0.2s ease;
}

    .action-icon:hover {
        color: #a62626;
        transform: scale(1.1);
    }

        .action-icon:hover i.far {
            font-weight: 900;
        }

    .action-icon.active {
        color: #a62626;
    }

.hidden-overlow-text {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
}